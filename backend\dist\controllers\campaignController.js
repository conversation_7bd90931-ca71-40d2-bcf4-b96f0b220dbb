"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCampaignByIdExport = exports.getPublicCampaignsExport = exports.updateCampaignStatusExport = exports.deleteCampaignExport = exports.updateCampaignExport = exports.getCampaignsExport = exports.createCampaignExport = exports.fixCampaignImages = exports.manualUpdateCampaignStatuses = exports.updateCampaignStatus = exports.deleteCampaign = exports.updateCampaign = exports.getCampaignById = exports.getPublicCampaigns = exports.getCampaigns = exports.createCampaign = void 0;
const Campaign_1 = require("../models/Campaign");
const Donation_1 = require("../models/Donation");
const user_model_1 = require("../models/user.model");
const notification_service_1 = require("../services/notification.service");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const cloudinary_1 = require("../services/cloudinary");
const mongoose_1 = __importDefault(require("mongoose"));
const upload_1 = require("../middleware/upload");
const pathUtils_1 = require("../utils/pathUtils");
// Helper function để xóa các ảnh cụ thể
const cleanupSpecificImages = (campaignId, imagePaths) => __awaiter(void 0, void 0, void 0, function* () {
    if (!imagePaths || imagePaths.length === 0) {
        console.log('📁 [Campaign] No specific images to cleanup');
        return;
    }
    console.log('🗑️ [Campaign] Cleaning up specific images:', imagePaths.length);
    for (const imagePath of imagePaths) {
        try {
            // Extract filename from path
            const filename = imagePath.split('/').pop();
            if (!filename)
                continue;
            // Construct full file path
            const uploadsDir = path_1.default.join(__dirname, '../../uploads');
            const campaignDir = path_1.default.join(uploadsDir, campaignId);
            const fullPath = path_1.default.join(campaignDir, filename);
            // Check if file exists and delete it
            if (fs_1.default.existsSync(fullPath)) {
                fs_1.default.unlinkSync(fullPath);
                console.log('🗑️ [Campaign] Deleted file:', fullPath);
            }
            else {
                console.log('⚠️ [Campaign] File not found for deletion:', fullPath);
            }
        }
        catch (error) {
            console.error('❌ [Campaign] Error deleting file:', imagePath, error);
        }
    }
});
var CampaignStatus;
(function (CampaignStatus) {
    CampaignStatus["DRAFT"] = "draft";
    CampaignStatus["ACTIVE"] = "active";
    CampaignStatus["COMPLETED"] = "completed";
    CampaignStatus["CANCELLED"] = "cancelled";
})(CampaignStatus || (CampaignStatus = {}));
// Giả định đã có model Campaign và middleware upload
// @desc    Create new campaign
// @route   POST /api/admin/campaigns
// @access  Private (admin)
const createCampaign = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🆕 [Campaign] Create campaign called');
        console.log('📝 [Campaign] Request body:', JSON.stringify(req.body, null, 2));
        console.log('📁 [Campaign] Files:', req.files ? req.files.length : 0);
        console.log('👤 [Campaign] User:', req.user ? req.user.name : 'No user');
        if (!req.user) {
            console.log('❌ [Campaign] Unauthorized - no user');
            return res.status(401).json({ message: 'Unauthorized' });
        }
        const { title, description, targetAmount, startDate, endDate, category } = req.body;
        // Validate required fields
        console.log('🔍 [Campaign] Validating fields:', { title, description, targetAmount, startDate, endDate, category });
        if (!title || !description || !targetAmount || !startDate || !endDate) {
            console.log('❌ [Campaign] Missing required fields');
            return res.status(400).json({ message: 'Missing required fields' });
        }
        // Validate dates
        const start = new Date(startDate);
        const end = new Date(endDate);
        if (start >= end) {
            return res.status(400).json({ message: 'End date must be after start date' });
        }
        // Create campaign first to get ID
        const tempCampaign = new Campaign_1.Campaign({
            title,
            description,
            targetAmount,
            startDate: start,
            endDate: end,
            category: category || 'Khác',
            images: [],
            image: '',
            createdBy: req.user._id,
            status: CampaignStatus.ACTIVE
        });
        console.log('💾 [Campaign] Saving campaign to get ID...');
        yield tempCampaign.save();
        const campaignId = tempCampaign._id.toString();
        console.log('✅ [Campaign] Campaign saved with ID:', campaignId);
        // Upload images to campaign-specific directory
        let imageUrls = [];
        if (req.files && req.files.length > 0) {
            console.log('📁 [Campaign] Processing files for campaign:', campaignId);
            // Check if Cloudinary is configured
            const hasCloudinaryConfig = process.env.CLOUDINARY_CLOUD_NAME &&
                process.env.CLOUDINARY_API_KEY &&
                process.env.CLOUDINARY_API_SECRET;
            if (!hasCloudinaryConfig) {
                console.log('⚠️ [Campaign] Cloudinary not configured, using local file paths');
                // Move files to campaign directory and update paths
                imageUrls = req.files.map(file => {
                    const campaignDir = (0, upload_1.createCampaignDir)(campaignId);
                    // Generate unique filename to avoid conflicts
                    const uniqueFilename = (0, pathUtils_1.generateUniqueFileName)(file.originalname);
                    const newPath = path_1.default.join(campaignDir, uniqueFilename);
                    // Move file to campaign directory
                    fs_1.default.renameSync(file.path, newPath);
                    // Create normalized web path
                    const relativePath = (0, pathUtils_1.normalizeWebPath)(`/uploads/${campaignId}/${uniqueFilename}`);
                    console.log('📁 [Campaign] Moved file to campaign directory:', relativePath);
                    console.log('📁 [Campaign] File system path:', newPath);
                    return relativePath;
                });
                console.log('✅ [Campaign] Using local images in campaign directory:', imageUrls.length);
            }
            else {
                try {
                    imageUrls = yield Promise.all(req.files.map(file => (0, cloudinary_1.uploadToCloudinary)(file.path)));
                    console.log('✅ [Campaign] Files uploaded to Cloudinary successfully:', imageUrls.length);
                }
                catch (uploadError) {
                    console.error('❌ [Campaign] Error uploading to Cloudinary:', uploadError);
                    // Fallback to local paths in campaign directory
                    console.log('⚠️ [Campaign] Falling back to local file paths in campaign directory');
                    imageUrls = req.files.map(file => {
                        const campaignDir = (0, upload_1.createCampaignDir)(campaignId);
                        // Generate unique filename to avoid conflicts
                        const uniqueFilename = (0, pathUtils_1.generateUniqueFileName)(file.originalname);
                        const newPath = path_1.default.join(campaignDir, uniqueFilename);
                        // Move file to campaign directory
                        fs_1.default.renameSync(file.path, newPath);
                        // Create normalized web path
                        const relativePath = (0, pathUtils_1.normalizeWebPath)(`/uploads/${campaignId}/${uniqueFilename}`);
                        console.log('📁 [Campaign] Fallback moved file to campaign directory:', relativePath);
                        console.log('📁 [Campaign] File system path:', newPath);
                        return relativePath;
                    });
                    console.log('✅ [Campaign] Using local images as fallback in campaign directory:', imageUrls.length);
                }
            }
            // Update campaign with images
            tempCampaign.images = imageUrls;
            tempCampaign.image = imageUrls.length > 0 ? imageUrls[0] : '';
            yield tempCampaign.save();
            console.log('✅ [Campaign] Updated campaign with images');
        }
        else {
            console.log('📁 [Campaign] No files to upload');
        }
        console.log('💾 [Campaign] Campaign creation completed with data:', {
            title,
            description,
            targetAmount,
            startDate: start,
            endDate: end,
            category: category || 'Khác',
            imagesCount: imageUrls.length,
            createdBy: req.user._id,
            campaignId: campaignId
        });
        // Send notification to all users about new campaign
        try {
            const allUsers = yield user_model_1.User.find({ role: 'user' }).select('_id');
            const userIds = allUsers.map(user => user._id);
            if (userIds.length > 0) {
                yield (0, notification_service_1.notifyNewCampaign)(userIds, tempCampaign.title, tempCampaign._id);
                console.log('📧 [Campaign] Notification sent to', userIds.length, 'users');
            }
        }
        catch (notificationError) {
            console.error('❌ [Campaign] Error sending notification:', notificationError);
            // Don't fail the campaign creation if notification fails
        }
        res.status(201).json(tempCampaign);
    }
    catch (error) {
        console.error('❌ [Campaign] Error creating campaign:', error);
        console.error('❌ [Campaign] Error stack:', error instanceof Error ? error.stack : 'No stack');
        res.status(500).json({
            message: 'Lỗi khi tạo chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.createCampaign = createCampaign;
// Helper function để cập nhật trạng thái chiến dịch
const updateCampaignStatuses = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const now = new Date();
        // Tạo endOfDay để so sánh - chiến dịch kết thúc vào cuối ngày endDate (UTC)
        const getEndOfDay = (date) => {
            const endOfDay = new Date(date);
            endOfDay.setUTCHours(23, 59, 59, 999);
            return endOfDay;
        };
        // Tìm các chiến dịch cần cập nhật trạng thái
        const campaignsToUpdate = yield Campaign_1.Campaign.find({
            $or: [
                // Chiến dịch đã hết hạn nhưng chưa completed (so sánh với cuối ngày)
                {
                    status: { $in: ['active', 'draft'] }
                },
                // Chiến dịch đã đạt mục tiêu nhưng chưa completed
                {
                    $expr: { $gte: ['$currentAmount', '$targetAmount'] },
                    status: { $in: ['active', 'draft'] }
                },
                // Chiến dịch đã bắt đầu nhưng vẫn là draft
                {
                    startDate: { $lte: now },
                    status: 'draft'
                }
            ]
        });
        for (const campaign of campaignsToUpdate) {
            const hasReachedTarget = campaign.currentAmount >= campaign.targetAmount;
            const endOfEndDate = getEndOfDay(campaign.endDate);
            const isExpired = now > endOfEndDate; // So sánh với cuối ngày endDate
            const shouldBeActive = now >= campaign.startDate && now <= endOfEndDate && !hasReachedTarget;
            let newStatus = campaign.status;
            if (hasReachedTarget || isExpired) {
                newStatus = 'completed';
            }
            else if (shouldBeActive && campaign.status === 'draft') {
                newStatus = 'active';
            }
            if (newStatus !== campaign.status) {
                const newProgress = Math.min(100, (campaign.currentAmount / campaign.targetAmount) * 100);
                yield Campaign_1.Campaign.updateOne({ _id: campaign._id }, {
                    $set: {
                        status: newStatus,
                        progress: newProgress,
                        updatedAt: new Date()
                    }
                });
                console.log(`✅ Updated campaign "${campaign.title}": ${campaign.status} → ${newStatus} ${hasReachedTarget ? '(reached target)' : isExpired ? '(expired)' : '(started)'}`);
            }
        }
    }
    catch (error) {
        console.error('Error updating campaign statuses:', error);
    }
});
// @desc    Get all campaigns
// @route   GET /api/admin/campaigns
// @access  Private (admin)
const getCampaigns = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Cập nhật trạng thái chiến dịch trước khi fetch
        yield updateCampaignStatuses();
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const [campaigns, total] = yield Promise.all([
            Campaign_1.Campaign.find()
                .populate('createdBy', 'name email')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            Campaign_1.Campaign.countDocuments()
        ]);
        res.json({
            success: true,
            campaigns,
            data: campaigns, // Add data field for consistency
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Error getting campaigns:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy danh sách chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getCampaigns = getCampaigns;
// @desc    Get public campaigns
// @route   GET /api/campaigns
// @access  Public
const getPublicCampaigns = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Cập nhật trạng thái chiến dịch trước khi fetch
        yield updateCampaignStatuses();
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        // Trả về cả chiến dịch active và completed để frontend có thể hiển thị trong 2 tab
        const [campaigns, total] = yield Promise.all([
            Campaign_1.Campaign.find({
                status: { $in: ['active', 'completed'] }
            })
                .populate('createdBy', 'name email')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            Campaign_1.Campaign.countDocuments({
                status: { $in: ['active', 'completed'] }
            })
        ]);
        res.json({
            success: true,
            data: campaigns,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Error getting public campaigns:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy danh sách chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getPublicCampaigns = getPublicCampaigns;
// @desc    Get campaign by ID
// @route   GET /api/campaigns/:id
// @access  Public
const getCampaignById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Cập nhật trạng thái chiến dịch trước khi fetch
        yield updateCampaignStatuses();
        const campaign = yield Campaign_1.Campaign.findById(req.params.id)
            .populate('createdBy', 'name email');
        if (!campaign) {
            return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
        }
        res.json(campaign);
    }
    catch (error) {
        console.error('Error getting campaign by ID:', error);
        res.status(500).json({
            message: 'Lỗi khi lấy thông tin chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getCampaignById = getCampaignById;
// @desc    Update campaign
// @route   PUT /api/admin/campaigns/:id
// @access  Private (admin)
const updateCampaign = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    console.log('🔄 [Campaign] ===== UPDATE CAMPAIGN START =====');
    console.log('🔄 [Campaign] Update campaign called for ID:', req.params.id);
    console.log('📝 [Campaign] Request body:', JSON.stringify(req.body, null, 2));
    console.log('📁 [Campaign] Files:', req.files ? req.files.length : 0);
    console.log('👤 [Campaign] User:', req.user ? req.user.name : 'No user');
    try {
        const { title, description, targetAmount, startDate, endDate, category, status, existingImages } = req.body;
        // Validate campaign ID
        if (!mongoose_1.default.Types.ObjectId.isValid(req.params.id)) {
            console.log('❌ [Campaign] Invalid campaign ID:', req.params.id);
            return res.status(400).json({ message: 'ID chiến dịch không hợp lệ' });
        }
        const campaign = yield Campaign_1.Campaign.findById(req.params.id);
        if (!campaign) {
            console.log('❌ [Campaign] Campaign not found:', req.params.id);
            return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
        }
        console.log('✅ [Campaign] Found campaign:', campaign.title);
        // Xử lý hình ảnh một cách thông minh
        let finalImages = [];
        const campaignId = req.params.id;
        console.log('📁 [Campaign] ===== IMAGE PROCESSING START =====');
        console.log('📁 [Campaign] Raw existingImages:', existingImages);
        console.log('📁 [Campaign] existingImages type:', typeof existingImages);
        console.log('📁 [Campaign] existingImages isArray:', Array.isArray(existingImages));
        console.log('📁 [Campaign] New files count:', req.files ? req.files.length : 0);
        // Xử lý danh sách ảnh hiện có được giữ lại
        let keptImages = [];
        if (existingImages) {
            if (Array.isArray(existingImages)) {
                keptImages = existingImages.filter(img => img && img.trim());
            }
            else if (typeof existingImages === 'string' && existingImages.trim()) {
                keptImages = [existingImages];
            }
        }
        console.log('📁 [Campaign] Images to keep:', keptImages.length, keptImages);
        // Lấy danh sách ảnh hiện tại từ database để so sánh
        const currentImages = campaign.images || [];
        console.log('📁 [Campaign] Current images in DB:', currentImages.length, currentImages);
        // Tìm các ảnh cần xóa (có trong DB nhưng không có trong keptImages)
        const imagesToDelete = currentImages.filter((img) => !keptImages.includes(img));
        console.log('📁 [Campaign] Images to delete:', imagesToDelete.length, imagesToDelete);
        // Xóa các file ảnh cũ không còn sử dụng
        yield cleanupSpecificImages(campaignId, imagesToDelete);
        // Thêm các ảnh được giữ lại vào finalImages
        finalImages = [...keptImages];
        // Upload và thêm hình ảnh mới nếu có
        if (req.files && req.files.length > 0) {
            console.log('📁 [Campaign] Uploading new images:', req.files.length);
            // Check if Cloudinary is configured
            const hasCloudinaryConfig = process.env.CLOUDINARY_CLOUD_NAME &&
                process.env.CLOUDINARY_API_KEY &&
                process.env.CLOUDINARY_API_SECRET;
            if (!hasCloudinaryConfig) {
                console.log('⚠️ [Campaign] Cloudinary not configured, using local file paths');
                // Ensure campaign directory exists
                const campaignDir = (0, upload_1.createCampaignDir)(campaignId);
                console.log('📁 [Campaign] Campaign directory:', campaignDir);
                // Process each new file
                const newImageUrls = req.files.map(file => {
                    // Generate unique filename to avoid conflicts
                    const uniqueFilename = (0, pathUtils_1.generateUniqueFileName)(file.originalname);
                    const newPath = path_1.default.join(campaignDir, uniqueFilename);
                    // Ensure source file exists before moving
                    if (!fs_1.default.existsSync(file.path)) {
                        console.error('❌ [Campaign] Source file not found:', file.path);
                        throw new Error(`Source file not found: ${file.path}`);
                    }
                    // Move file to campaign directory
                    fs_1.default.renameSync(file.path, newPath);
                    // Verify file was moved successfully
                    if (!fs_1.default.existsSync(newPath)) {
                        console.error('❌ [Campaign] Failed to move file to:', newPath);
                        throw new Error(`Failed to move file to: ${newPath}`);
                    }
                    // Create normalized web path
                    const relativePath = (0, pathUtils_1.normalizeWebPath)(`/uploads/${campaignId}/${uniqueFilename}`);
                    console.log('📁 [Campaign] Successfully moved file:', {
                        originalName: file.originalname,
                        uniqueFilename: uniqueFilename,
                        sourcePath: file.path,
                        destinationPath: newPath,
                        webPath: relativePath
                    });
                    return relativePath;
                });
                finalImages = [...finalImages, ...newImageUrls];
                console.log('✅ [Campaign] Added new local images:', newImageUrls.length);
                console.log('📁 [Campaign] Total images after adding new ones:', finalImages.length);
            }
            else {
                try {
                    const newImageUrls = yield Promise.all(req.files.map((file) => __awaiter(void 0, void 0, void 0, function* () {
                        console.log('📁 [Campaign] Uploading file to Cloudinary:', file.path);
                        const url = yield (0, cloudinary_1.uploadToCloudinary)(file.path);
                        console.log('✅ [Campaign] File uploaded to Cloudinary:', url);
                        return url;
                    })));
                    finalImages = [...finalImages, ...newImageUrls];
                    console.log('✅ [Campaign] New images uploaded to Cloudinary successfully:', newImageUrls.length);
                }
                catch (uploadError) {
                    console.error('❌ [Campaign] Error uploading new images to Cloudinary:', uploadError);
                    console.error('❌ [Campaign] Upload error stack:', uploadError instanceof Error ? uploadError.stack : 'No stack');
                    // Fallback to local paths in campaign directory
                    console.log('⚠️ [Campaign] Falling back to local file paths in campaign directory');
                    // Ensure campaign directory exists
                    const campaignDir = (0, upload_1.createCampaignDir)(campaignId);
                    console.log('📁 [Campaign] Fallback campaign directory:', campaignDir);
                    const newImageUrls = req.files.map(file => {
                        // Generate unique filename to avoid conflicts
                        const uniqueFilename = (0, pathUtils_1.generateUniqueFileName)(file.originalname);
                        const newPath = path_1.default.join(campaignDir, uniqueFilename);
                        // Ensure source file exists before moving
                        if (!fs_1.default.existsSync(file.path)) {
                            console.error('❌ [Campaign] Fallback: Source file not found:', file.path);
                            throw new Error(`Fallback: Source file not found: ${file.path}`);
                        }
                        // Move file to campaign directory
                        fs_1.default.renameSync(file.path, newPath);
                        // Verify file was moved successfully
                        if (!fs_1.default.existsSync(newPath)) {
                            console.error('❌ [Campaign] Fallback: Failed to move file to:', newPath);
                            throw new Error(`Fallback: Failed to move file to: ${newPath}`);
                        }
                        // Create normalized web path
                        const relativePath = (0, pathUtils_1.normalizeWebPath)(`/uploads/${campaignId}/${uniqueFilename}`);
                        console.log('📁 [Campaign] Fallback successfully moved file:', {
                            originalName: file.originalname,
                            uniqueFilename: uniqueFilename,
                            sourcePath: file.path,
                            destinationPath: newPath,
                            webPath: relativePath
                        });
                        return relativePath;
                    });
                    finalImages = [...finalImages, ...newImageUrls];
                    console.log('✅ [Campaign] Added fallback local images:', newImageUrls.length);
                    console.log('📁 [Campaign] Total images after fallback:', finalImages.length);
                }
            }
        }
        // Prepare update object
        const updateData = {};
        if (title) {
            updateData.title = title;
            console.log('📝 [Campaign] Updated title:', title);
        }
        if (description) {
            updateData.description = description;
            console.log('📝 [Campaign] Updated description length:', description.length);
        }
        if (targetAmount) {
            updateData.targetAmount = Number(targetAmount);
            console.log('📝 [Campaign] Updated targetAmount:', targetAmount);
        }
        if (startDate) {
            updateData.startDate = new Date(startDate);
            console.log('📝 [Campaign] Updated startDate:', startDate);
        }
        if (endDate) {
            updateData.endDate = new Date(endDate);
            console.log('📝 [Campaign] Updated endDate:', endDate);
        }
        if (category) {
            updateData.category = category;
            console.log('📝 [Campaign] Updated category:', category);
        }
        if (status) {
            updateData.status = status;
            console.log('📝 [Campaign] Updated status:', status);
        }
        // Update images - always update to reflect current state
        updateData.images = finalImages;
        updateData.image = finalImages.length > 0 ? finalImages[0] : '';
        console.log('📁 [Campaign] ===== IMAGE PROCESSING COMPLETE =====');
        console.log('📁 [Campaign] Final images count:', finalImages.length);
        console.log('📁 [Campaign] Final images array:', finalImages);
        console.log('📁 [Campaign] Primary image:', updateData.image);
        // Verify image integrity and cleanup orphaned files
        const integrity = (0, pathUtils_1.verifyImageIntegrity)(campaignId, finalImages);
        console.log('📁 [Campaign] Image integrity check:', {
            valid: integrity.valid.length,
            invalid: integrity.invalid.length,
            missing: integrity.missing.length
        });
        if (integrity.invalid.length > 0) {
            console.warn('⚠️ [Campaign] Invalid image paths found:', integrity.invalid);
        }
        if (integrity.missing.length > 0) {
            console.warn('⚠️ [Campaign] Missing image files:', integrity.missing);
            // Remove missing images from finalImages
            finalImages = integrity.valid;
            updateData.images = finalImages;
            updateData.image = finalImages.length > 0 ? finalImages[0] : '';
        }
        // Cleanup orphaned files
        (0, pathUtils_1.cleanupOrphanedImages)(campaignId, finalImages);
        console.log('📁 [Campaign] Final verified images:', finalImages.length);
        console.log('💾 [Campaign] Updating campaign with data:', Object.keys(updateData));
        const updatedCampaign = yield Campaign_1.Campaign.findByIdAndUpdate(req.params.id, updateData, { new: true, runValidators: false } // Skip validation to avoid createdBy issue
        );
        if (!updatedCampaign) {
            return res.status(404).json({ message: 'Không tìm thấy chiến dịch sau khi cập nhật' });
        }
        console.log('✅ [Campaign] Campaign updated successfully');
        res.json(updatedCampaign);
    }
    catch (error) {
        console.error('❌ [Campaign] ===== UPDATE CAMPAIGN ERROR =====');
        console.error('❌ [Campaign] Error updating campaign:', error);
        console.error('❌ [Campaign] Error stack:', error instanceof Error ? error.stack : 'No stack');
        console.error('❌ [Campaign] ===== END ERROR =====');
        res.status(500).json({
            message: 'Lỗi khi cập nhật chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.updateCampaign = updateCampaign;
// @desc    Delete campaign
// @route   DELETE /api/admin/campaigns/:id
// @access  Private (admin)
const deleteCampaign = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { reason } = req.body; // Get deletion reason from request body
        const campaign = yield Campaign_1.Campaign.findById(req.params.id);
        if (!campaign) {
            return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
        }
        // Find all donors who donated to this campaign
        try {
            const donations = yield Donation_1.Donation.find({
                campaignId: campaign._id,
                status: 'completed'
            }).distinct('userId');
            if (donations.length > 0) {
                yield (0, notification_service_1.notifyCampaignDeleted)(donations, campaign.title, campaign._id, reason || 'Chiến dịch đã bị hủy bởi quản trị viên');
                console.log('📧 [Campaign] Deletion notification sent to', donations.length, 'donors');
            }
        }
        catch (notificationError) {
            console.error('❌ [Campaign] Error sending deletion notification:', notificationError);
            // Don't fail the deletion if notification fails
        }
        // Cleanup campaign directory
        (0, upload_1.cleanupCampaignDir)(campaign._id.toString());
        yield campaign.deleteOne();
        res.json({ message: 'Chiến dịch đã được xóa' });
    }
    catch (error) {
        console.error('Error deleting campaign:', error);
        res.status(500).json({
            message: 'Lỗi khi xóa chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.deleteCampaign = deleteCampaign;
// @desc    Update campaign status
// @route   PATCH /api/admin/campaigns/:id/status
// @access  Private (admin)
const updateCampaignStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { status } = req.body;
        if (!status || !Object.values(CampaignStatus).includes(status)) {
            return res.status(400).json({ message: 'Invalid status' });
        }
        const campaign = yield Campaign_1.Campaign.findById(req.params.id);
        if (!campaign) {
            return res.status(404).json({ message: 'Không tìm thấy chiến dịch' });
        }
        campaign.status = status;
        yield campaign.save();
        res.json(campaign);
    }
    catch (error) {
        console.error('Error updating campaign status:', error);
        res.status(500).json({
            message: 'Lỗi khi cập nhật trạng thái chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.updateCampaignStatus = updateCampaignStatus;
// @desc    Manual update campaign statuses
// @route   POST /api/admin/campaigns/update-statuses
// @access  Private (admin)
const manualUpdateCampaignStatuses = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔄 Manual campaign status update triggered by admin');
        yield updateCampaignStatuses();
        // Get updated counts
        const [activeCount, completedCount, draftCount] = yield Promise.all([
            Campaign_1.Campaign.countDocuments({ status: 'active' }),
            Campaign_1.Campaign.countDocuments({ status: 'completed' }),
            Campaign_1.Campaign.countDocuments({ status: 'draft' })
        ]);
        res.json({
            success: true,
            message: 'Trạng thái chiến dịch đã được cập nhật',
            counts: {
                active: activeCount,
                completed: completedCount,
                draft: draftCount
            }
        });
    }
    catch (error) {
        console.error('Error in manual campaign status update:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi cập nhật trạng thái chiến dịch',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.manualUpdateCampaignStatuses = manualUpdateCampaignStatuses;
// @desc    Fix campaign images paths
// @route   POST /api/campaigns/admin/fix-images
// @access  Private (admin)
const fixCampaignImages = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔧 [Campaign] Starting image path fix...');
        const fs = yield Promise.resolve().then(() => __importStar(require('fs')));
        const path = yield Promise.resolve().then(() => __importStar(require('path')));
        // Get all campaigns
        const campaigns = yield Campaign_1.Campaign.find({});
        console.log(`📊 [Campaign] Found ${campaigns.length} campaigns to check`);
        // Get all files in uploads directory
        const uploadsDir = path.join(__dirname, '../uploads');
        const files = fs.readdirSync(uploadsDir).filter(file => file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif'));
        console.log(`📁 [Campaign] Found ${files.length} image files in uploads`);
        let fixedCount = 0;
        for (const campaign of campaigns) {
            let hasChanges = false;
            const newImages = [];
            for (const imagePath of campaign.images) {
                // Extract filename from path
                const filename = imagePath.split('/').pop() || imagePath;
                // Check if file exists in uploads
                if (files.includes(filename)) {
                    newImages.push(`/uploads/${filename}`);
                    console.log(`✅ [Campaign] Fixed image path: ${imagePath} -> /uploads/${filename}`);
                    hasChanges = true;
                }
                else {
                    // Try to find similar filename
                    const similarFile = files.find(file => file.includes(filename.split('-')[0]) ||
                        filename.includes(file.split('-')[0]));
                    if (similarFile) {
                        newImages.push(`/uploads/${similarFile}`);
                        console.log(`🔄 [Campaign] Mapped image: ${imagePath} -> /uploads/${similarFile}`);
                        hasChanges = true;
                    }
                    else {
                        console.log(`❌ [Campaign] No matching file found for: ${imagePath}`);
                        // Don't include this image
                    }
                }
            }
            if (hasChanges) {
                campaign.images = newImages;
                yield campaign.save();
                fixedCount++;
                console.log(`✅ [Campaign] Fixed campaign: ${campaign.title}`);
            }
        }
        console.log(`🎉 [Campaign] Fixed ${fixedCount} campaigns`);
        res.json({
            success: true,
            message: `Fixed image paths for ${fixedCount} campaigns`,
            totalCampaigns: campaigns.length,
            fixedCampaigns: fixedCount,
            availableFiles: files
        });
    }
    catch (error) {
        console.error('❌ [Campaign] Error fixing images:', error);
        res.status(500).json({
            success: false,
            message: 'Error fixing campaign images',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.fixCampaignImages = fixCampaignImages;
exports.createCampaignExport = exports.createCampaign;
exports.getCampaignsExport = exports.getCampaigns;
exports.updateCampaignExport = exports.updateCampaign;
exports.deleteCampaignExport = exports.deleteCampaign;
exports.updateCampaignStatusExport = exports.updateCampaignStatus;
exports.getPublicCampaignsExport = exports.getPublicCampaigns;
exports.getCampaignByIdExport = exports.getCampaignById;
