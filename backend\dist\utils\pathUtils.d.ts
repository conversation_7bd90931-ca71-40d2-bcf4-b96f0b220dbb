/**
 * Normalize file paths for web URLs
 * Converts Windows backslashes to forward slashes and removes double slashes
 */
export declare function normalizeWebPath(filePath: string): string;
/**
 * Create a safe file path for the file system
 * Ensures proper directory separators for the current OS
 */
export declare function createSafeFilePath(basePath: string, ...segments: string[]): string;
/**
 * Convert a file system path to a web URL path
 */
export declare function filePathToWebPath(filePath: string, uploadsDir: string): string;
/**
 * Validate and clean image file name
 */
export declare function cleanImageFileName(fileName: string): string;
/**
 * Generate a unique filename with timestamp
 */
export declare function generateUniqueFileName(originalName: string): string;
/**
 * Verify image file integrity and paths
 */
export declare function verifyImageIntegrity(campaignId: string, imagePaths: string[]): {
    valid: string[];
    invalid: string[];
    missing: string[];
};
/**
 * Clean up orphaned image files
 */
export declare function cleanupOrphanedImages(campaignId: string, validImagePaths: string[]): void;
//# sourceMappingURL=pathUtils.d.ts.map