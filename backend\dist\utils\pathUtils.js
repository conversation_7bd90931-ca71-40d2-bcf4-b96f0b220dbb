"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeWebPath = normalizeWebPath;
exports.createSafeFilePath = createSafeFilePath;
exports.filePathToWebPath = filePathToWebPath;
exports.cleanImageFileName = cleanImageFileName;
exports.generateUniqueFileName = generateUniqueFileName;
exports.verifyImageIntegrity = verifyImageIntegrity;
exports.cleanupOrphanedImages = cleanupOrphanedImages;
const path_1 = __importDefault(require("path"));
/**
 * Normalize file paths for web URLs
 * Converts Windows backslashes to forward slashes and removes double slashes
 */
function normalizeWebPath(filePath) {
    if (!filePath)
        return '';
    // Convert backslashes to forward slashes
    let normalized = filePath.replace(/\\/g, '/');
    // Remove double slashes (except for protocol://)
    normalized = normalized.replace(/([^:]\/)\/+/g, '$1');
    // Ensure path starts with single slash if it's a relative path
    if (normalized.startsWith('/uploads') && !normalized.startsWith('//')) {
        return normalized;
    }
    // If path doesn't start with /uploads, add it
    if (!normalized.startsWith('/uploads') && !normalized.startsWith('http')) {
        normalized = `/uploads/${normalized.replace(/^\/+/, '')}`;
    }
    return normalized;
}
/**
 * Create a safe file path for the file system
 * Ensures proper directory separators for the current OS
 */
function createSafeFilePath(basePath, ...segments) {
    // Join all segments with proper OS separators
    const fullPath = path_1.default.join(basePath, ...segments);
    // Ensure the directory exists
    const dir = path_1.default.dirname(fullPath);
    const fs = require('fs');
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    return fullPath;
}
/**
 * Convert a file system path to a web URL path
 */
function filePathToWebPath(filePath, uploadsDir) {
    if (!filePath)
        return '';
    // Get relative path from uploads directory
    const relativePath = path_1.default.relative(uploadsDir, filePath);
    // Convert to web path
    const webPath = normalizeWebPath(`/uploads/${relativePath}`);
    return webPath;
}
/**
 * Validate and clean image file name
 */
function cleanImageFileName(fileName) {
    if (!fileName)
        return '';
    // Remove any path separators and keep only the filename
    const cleanName = path_1.default.basename(fileName);
    // Remove any potentially dangerous characters
    return cleanName.replace(/[^a-zA-Z0-9.-]/g, '_');
}
/**
 * Generate a unique filename with timestamp
 */
function generateUniqueFileName(originalName) {
    const ext = path_1.default.extname(originalName);
    const name = path_1.default.basename(originalName, ext);
    const timestamp = Date.now();
    return `${cleanImageFileName(name)}-${timestamp}${ext}`;
}
/**
 * Verify image file integrity and paths
 */
function verifyImageIntegrity(campaignId, imagePaths) {
    const fs = require('fs');
    const uploadsDir = path_1.default.join(__dirname, '../uploads');
    const campaignDir = path_1.default.join(uploadsDir, campaignId);
    const result = {
        valid: [],
        invalid: [],
        missing: []
    };
    for (const imagePath of imagePaths) {
        if (!imagePath || !imagePath.trim()) {
            result.invalid.push(imagePath);
            continue;
        }
        // Extract filename from path
        const filename = imagePath.split('/').pop();
        if (!filename) {
            result.invalid.push(imagePath);
            continue;
        }
        // Check if file exists
        const fullPath = path_1.default.join(campaignDir, filename);
        if (fs.existsSync(fullPath)) {
            result.valid.push(imagePath);
        }
        else {
            result.missing.push(imagePath);
        }
    }
    return result;
}
/**
 * Clean up orphaned image files
 */
function cleanupOrphanedImages(campaignId, validImagePaths) {
    const fs = require('fs');
    const uploadsDir = path_1.default.join(__dirname, '../uploads');
    const campaignDir = path_1.default.join(uploadsDir, campaignId);
    if (!fs.existsSync(campaignDir)) {
        return;
    }
    // Get all files in campaign directory
    const allFiles = fs.readdirSync(campaignDir);
    // Get filenames from valid image paths
    const validFilenames = validImagePaths.map(imagePath => imagePath.split('/').pop()).filter(Boolean);
    // Find orphaned files
    const orphanedFiles = allFiles.filter(file => !validFilenames.includes(file));
    // Delete orphaned files
    for (const orphanedFile of orphanedFiles) {
        try {
            const filePath = path_1.default.join(campaignDir, orphanedFile);
            fs.unlinkSync(filePath);
            console.log(`🗑️ [PathUtils] Cleaned up orphaned file: ${orphanedFile}`);
        }
        catch (error) {
            console.error(`❌ [PathUtils] Error cleaning up orphaned file ${orphanedFile}:`, error);
        }
    }
}
