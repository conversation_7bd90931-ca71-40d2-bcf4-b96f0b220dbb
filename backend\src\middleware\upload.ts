import multer from 'multer';
import path from 'path';
import fs from 'fs';

// <PERSON><PERSON><PERSON> b<PERSON><PERSON> thư mụ<PERSON> uploads tồn tại
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Helper function để tạo thư mục campaign
const createCampaignDir = (campaignId: string): string => {
  const campaignDir = path.join(uploadDir, campaignId);
  if (!fs.existsSync(campaignDir)) {
    fs.mkdirSync(campaignDir, { recursive: true });
    console.log('📁 [Upload] Created campaign directory:', campaignDir);
  }
  return campaignDir;
};

// Helper function để xóa thư mục campaign cũ
const cleanupCampaignDir = (campaignId: string): void => {
  try {
    const campaignDir = path.join(uploadDir, campaignId);

    // Validate campaignId to prevent directory traversal
    if (!campaignId || campaignId.includes('..') || campaignId.includes('/') || campaignId.includes('\\')) {
      console.error('❌ [Upload] Invalid campaign ID for cleanup:', campaignId);
      return;
    }

    if (fs.existsSync(campaignDir)) {
      // List files before deletion for logging
      const files = fs.readdirSync(campaignDir);
      console.log(`🗑️ [Upload] Cleaning up campaign directory: ${campaignDir} (${files.length} files)`);

      // Delete each file individually for better error handling
      for (const file of files) {
        const filePath = path.join(campaignDir, file);
        try {
          fs.unlinkSync(filePath);
          console.log(`🗑️ [Upload] Deleted file: ${file}`);
        } catch (fileError) {
          console.error(`❌ [Upload] Error deleting file ${file}:`, fileError);
        }
      }

      // Remove the directory
      fs.rmdirSync(campaignDir);
      console.log('✅ [Upload] Campaign directory cleaned up successfully');
    } else {
      console.log('⚠️ [Upload] Campaign directory does not exist:', campaignDir);
    }
  } catch (error) {
    console.error('❌ [Upload] Error cleaning up campaign directory:', error);
  }
};

// Cấu hình storage cho multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    try {
      console.log('📁 [Upload] Processing file upload for path:', req.path, 'method:', req.method);

      // Kiểm tra nếu là campaign upload
      if (req.path.includes('/campaigns/')) {
        // Lấy campaign ID từ URL hoặc body
        const campaignId = req.params.id || req.body.campaignId || `temp-${Date.now()}`;
        console.log('📁 [Upload] Campaign ID:', campaignId);

        // Tạo thư mục cho campaign
        const campaignDir = createCampaignDir(campaignId);
        console.log('📁 [Upload] Using campaign directory:', campaignDir);
        cb(null, campaignDir);
      } else {
        // Sử dụng thư mục uploads chính cho các upload khác
        console.log('📁 [Upload] Using main upload directory:', uploadDir);
        cb(null, uploadDir);
      }
    } catch (error) {
      console.error('❌ [Upload] Error in destination function:', error);
      cb(error as Error, uploadDir); // Fallback to default directory
    }
  },
  filename: function (req, file, cb) {
    // Tạo tên file duy nhất với timestamp
    const uniqueSuffix = Date.now();
    const filename = file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname);
    console.log('📁 [Upload] Generated filename:', filename);
    cb(null, filename);
  }
});

// Kiểm tra file type
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  console.log('🔍 [Upload] Checking file type:', file.mimetype, 'for file:', file.originalname);

  // Chỉ chấp nhận các file ảnh
  if (file.mimetype.startsWith('image/')) {
    console.log('✅ [Upload] File type accepted:', file.mimetype);
    cb(null, true);
  } else {
    console.log('❌ [Upload] File type rejected:', file.mimetype);
    cb(new Error('Chỉ chấp nhận file ảnh!'));
  }
};

// Cấu hình multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // Giới hạn 5MB
    files: 5 // Tối đa 5 file
  }
});

export default upload;
export { createCampaignDir, cleanupCampaignDir };