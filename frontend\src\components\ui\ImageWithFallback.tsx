import React, { useState, useEffect } from 'react';
import { getImageUrl, getFallbackUrls } from '../../utils/imageUtils';

interface ImageWithFallbackProps {
  src: string;
  alt: string;
  className?: string;
  fallbackText?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  alt,
  className = '',
  fallbackText = 'Hình ảnh không tải được',
  onLoad,
  onError
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [hasError, setHasError] = useState<boolean>(false);
  const [attemptCount, setAttemptCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);



  // Initialize current source
  useEffect(() => {
    if (src) {
      setCurrentSrc(getImageUrl(src));
      setHasError(false);
      setAttemptCount(0);
      setIsLoading(true);
    } else {
      setHasError(true);
      setIsLoading(false);
    }
  }, [src]);

  const handleImageError = () => {
    // Silent error handling - no console spam

    // Prevent infinite loops - max 2 attempts only
    if (attemptCount >= 2) {
      setHasError(true);
      setIsLoading(false);
      onError?.();
      return;
    }

    // Try fallback URLs
    const fallbackUrls = getFallbackUrls(currentSrc);

    if (attemptCount < fallbackUrls.length) {
      const fallbackUrl = fallbackUrls[attemptCount];

      // Avoid trying the same URL again
      if (fallbackUrl !== currentSrc) {
        setAttemptCount(prev => prev + 1);
        setIsLoading(true);
        // Add delay to prevent rapid fire requests
        setTimeout(() => {
          setCurrentSrc(fallbackUrl);
        }, 200);
        return;
      }
    }

    // All fallbacks failed or no more unique URLs
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  const handleImageLoad = () => {
    // Silent success - no console spam
    setHasError(false);
    setIsLoading(false);
    onLoad?.();
  };

  if (hasError) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        {fallbackText}
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className={`absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
          <div className="text-gray-400 text-xs">Đang tải...</div>
        </div>
      )}
      <img
        src={currentSrc}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ display: hasError ? 'none' : 'block' }}
      />
    </div>
  );
};

export default ImageWithFallback;
