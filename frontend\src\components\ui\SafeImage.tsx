import React from 'react';
import { getImageUrl } from '../../utils/imageUtils';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({ src, alt, className = '' }) => {
  const imageUrl = getImageUrl(src);

  if (!imageUrl) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        Không có hình ảnh
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      className={className}
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';

        // Create placeholder if not exists
        if (!target.nextElementSibling?.classList.contains('error-placeholder')) {
          const placeholder = document.createElement('div');
          placeholder.className = `error-placeholder bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`;
          placeholder.textContent = 'Hình ảnh không tải được';
          target.parentNode?.appendChild(placeholder);
        }
      }}
    />
  );
};

export default SafeImage;
