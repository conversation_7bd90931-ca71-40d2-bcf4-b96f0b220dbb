import React, { useState } from 'react';
import { getImageUrl } from '../../utils/imageUtils';

interface SimpleImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackText?: string;
}

const SimpleImage: React.FC<SimpleImageProps> = ({
  src,
  alt,
  className = '',
  fallbackText = 'H<PERSON><PERSON>nh không tải được'
}) => {
  const [hasError, setHasError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);



  const handleImageError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setHasError(false);
    setIsLoading(false);
  };

  const imageUrl = getImageUrl(src);

  if (!src || hasError) {
    return (
      <div className={`bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center text-gray-500 text-sm ${className}`}>
        {fallbackText}
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className={`absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
          <div className="text-gray-400 text-xs">Đang tải...</div>
        </div>
      )}
      <img
        src={imageUrl}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ display: hasError ? 'none' : 'block' }}
      />
    </div>
  );
};

export default SimpleImage;
