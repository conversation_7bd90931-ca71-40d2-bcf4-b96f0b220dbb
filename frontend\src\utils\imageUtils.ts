/**
 * Centralized image URL utility to handle all image path processing
 * This ensures consistent URL handling across the entire application
 */

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

/**
 * Process image path and return a clean, valid URL
 * @param imagePath - The image path from database or user input
 * @returns Clean URL ready for use in img src
 */
export function getImageUrl(imagePath: string): string {
  // Return empty string for falsy values
  if (!imagePath || imagePath.trim() === '') {
    return '';
  }

  const cleanPath = imagePath.trim();

  // Handle data URLs (base64 images) - return as is
  if (cleanPath.startsWith('data:')) {
    return cleanPath;
  }

  // Handle blob URLs - return as is
  if (cleanPath.startsWith('blob:')) {
    return cleanPath;
  }

  // Handle full HTTP URLs - return as is but clean up double slashes
  if (cleanPath.startsWith('http')) {
    return cleanDoubleSlashes(cleanPath);
  }

  // Process relative paths
  let processedPath = cleanPath;

  // Remove multiple slashes and normalize
  processedPath = processedPath.replace(/\/+/g, '/');

  // Ensure path starts with single slash
  if (!processedPath.startsWith('/')) {
    processedPath = '/' + processedPath;
  }

  // Construct final URL
  const finalUrl = `${API_URL}${processedPath}`;

  // Clean up any double slashes in the result
  return cleanDoubleSlashes(finalUrl);
}

/**
 * Clean double slashes from URL while preserving protocol
 * @param url - URL to clean
 * @returns Clean URL without double slashes
 */
function cleanDoubleSlashes(url: string): string {
  // First, protect the protocol
  const protocolMatch = url.match(/^(https?:\/\/)/);
  const protocol = protocolMatch ? protocolMatch[1] : '';
  
  // Remove protocol temporarily
  let cleanUrl = protocolMatch ? url.substring(protocol.length) : url;
  
  // Replace multiple slashes with single slash
  cleanUrl = cleanUrl.replace(/\/+/g, '/');
  
  // Restore protocol
  return protocol + cleanUrl;
}

/**
 * Validate if an image URL is accessible
 * @param url - URL to validate
 * @returns Promise<boolean> - true if image is accessible
 */
export function validateImageUrl(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }

    const img = new Image();
    
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    
    // Set timeout to avoid hanging
    setTimeout(() => resolve(false), 5000);
    
    img.src = url;
  });
}

/**
 * Get fallback URLs for an image
 * @param originalUrl - Original image URL
 * @returns Array of fallback URLs to try
 */
export function getFallbackUrls(originalUrl: string): string[] {
  const fallbacks: string[] = [];

  if (!originalUrl || !originalUrl.includes('/uploads/')) {
    return fallbacks;
  }

  const filename = originalUrl.split('/').pop() || '';

  // If filename has timestamp, try without timestamp
  if (filename.includes('-') && filename.split('-').length > 2) {
    const parts = filename.split('-');
    const simpleFilename = `${parts[0]}-${parts[1]}.jpg`;
    fallbacks.push(getImageUrl(`/uploads/${simpleFilename}`));
  }

  // Try with different extensions
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
  fallbacks.push(getImageUrl(`/uploads/${nameWithoutExt}.png`));
  fallbacks.push(getImageUrl(`/uploads/${nameWithoutExt}.jpeg`));
  fallbacks.push(getImageUrl(`/uploads/${nameWithoutExt}.gif`));

  return fallbacks;
}
