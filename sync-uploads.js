/**
 * Script đ<PERSON> đồng bộ file từ dist/uploads sang uploads
 */

const fs = require('fs');
const path = require('path');

function copyDirectory(src, dest) {
  // T<PERSON><PERSON> thư mục đích nếu chưa tồn tại
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  // Đọc tất cả file và thư mục trong thư mục nguồn
  const items = fs.readdirSync(src);

  items.forEach(item => {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);

    const stat = fs.statSync(srcPath);

    if (stat.isDirectory()) {
      // Nếu là thư mục, copy đệ quy
      copyDirectory(srcPath, destPath);
    } else {
      // Nếu là file, copy file
      fs.copyFileSync(srcPath, destPath);
      console.log(`Copied: ${srcPath} -> ${destPath}`);
    }
  });
}

// Đồng bộ từ backend/dist/uploads sang backend/uploads
const srcDir = path.join(__dirname, 'backend', 'dist', 'uploads');
const destDir = path.join(__dirname, 'backend', 'uploads');

console.log('🔄 Starting sync from dist/uploads to uploads...');
console.log(`Source: ${srcDir}`);
console.log(`Destination: ${destDir}`);

if (fs.existsSync(srcDir)) {
  copyDirectory(srcDir, destDir);
  console.log('✅ Sync completed successfully!');
} else {
  console.log('❌ Source directory does not exist:', srcDir);
}

// Đồng bộ campaign cụ thể
const campaignId = '682f1a49d928a1ef4e76f884';
const srcCampaignDir = path.join(__dirname, 'backend', 'dist', 'uploads', campaignId);
const destCampaignDir = path.join(__dirname, 'backend', 'uploads', campaignId);

console.log(`\n🔄 Syncing campaign ${campaignId}...`);
console.log(`Source: ${srcCampaignDir}`);
console.log(`Destination: ${destCampaignDir}`);

if (fs.existsSync(srcCampaignDir)) {
  copyDirectory(srcCampaignDir, destCampaignDir);
  console.log(`✅ Campaign ${campaignId} sync completed!`);
  
  // List files in destination
  const files = fs.readdirSync(destCampaignDir);
  console.log(`📁 Files in destination (${files.length}):`);
  files.forEach(file => {
    console.log(`  - ${file}`);
  });
} else {
  console.log(`❌ Campaign directory does not exist: ${srcCampaignDir}`);
}
