/**
 * Test script để kiểm tra hệ thống quản lý hình <PERSON>nh campaign
 */

const fs = require('fs');
const path = require('path');

// Simulate the image management functions
function normalizeWebPath(filePath) {
  if (!filePath) return '';
  
  // Convert backslashes to forward slashes
  let normalized = filePath.replace(/\\/g, '/');
  
  // Remove double slashes (except for protocol://)
  normalized = normalized.replace(/([^:]\/)\/+/g, '$1');
  
  // Ensure path starts with single slash if it's a relative path
  if (normalized.startsWith('/uploads') && !normalized.startsWith('//')) {
    return normalized;
  }
  
  // If path doesn't start with /uploads, add it
  if (!normalized.startsWith('/uploads') && !normalized.startsWith('http')) {
    normalized = `/uploads/${normalized.replace(/^\/+/, '')}`;
  }
  
  return normalized;
}

function generateUniqueFileName(originalName) {
  const ext = path.extname(originalName);
  const name = path.basename(originalName, ext);
  const timestamp = Date.now();
  
  return `${name.replace(/[^a-zA-Z0-9.-]/g, '_')}-${timestamp}${ext}`;
}

function verifyImageIntegrity(campaignId, imagePaths, uploadsDir = './backend/uploads') {
  const campaignDir = path.join(uploadsDir, campaignId);
  
  const result = {
    valid: [],
    invalid: [],
    missing: []
  };
  
  for (const imagePath of imagePaths) {
    if (!imagePath || !imagePath.trim()) {
      result.invalid.push(imagePath);
      continue;
    }
    
    // Extract filename from path
    const filename = imagePath.split('/').pop();
    if (!filename) {
      result.invalid.push(imagePath);
      continue;
    }
    
    // Check if file exists
    const fullPath = path.join(campaignDir, filename);
    if (fs.existsSync(fullPath)) {
      result.valid.push(imagePath);
    } else {
      result.missing.push(imagePath);
    }
  }
  
  return result;
}

// Test scenarios
console.log('🧪 Testing Image Management System');
console.log('=====================================');

// Test 1: Path normalization
console.log('\n1. Testing Path Normalization:');
const testPaths = [
  'campaign123\\image1.jpg',
  '/uploads/campaign123/image2.jpg',
  '//uploads/campaign123/image3.jpg',
  'uploads/campaign123/image4.jpg',
  'image5.jpg'
];

testPaths.forEach(testPath => {
  const normalized = normalizeWebPath(testPath);
  console.log(`  ${testPath} -> ${normalized}`);
});

// Test 2: Unique filename generation
console.log('\n2. Testing Unique Filename Generation:');
const testFiles = [
  'image.jpg',
  'my photo.png',
  'special@#$%chars.gif',
  'file-with-dashes.jpeg'
];

testFiles.forEach(filename => {
  const unique = generateUniqueFileName(filename);
  console.log(`  ${filename} -> ${unique}`);
});

// Test 3: Image integrity verification
console.log('\n3. Testing Image Integrity Verification:');
const testCampaignId = '682f1a49d928a1ef4e76f884';
const testImagePaths = [
  '/uploads/682f1a49d928a1ef4e76f884/images-1749117681866.jpg',
  '/uploads/682f1a49d928a1ef4e76f884/images-1749117681890.jpg',
  '/uploads/682f1a49d928a1ef4e76f884/images-1749117681893.jpg',
  '/uploads/682f1a49d928a1ef4e76f884/nonexistent.jpg',
  '',
  null
];

const integrity = verifyImageIntegrity(testCampaignId, testImagePaths.filter(Boolean));
console.log('  Integrity Check Results:');
console.log(`    Valid: ${integrity.valid.length} images`);
console.log(`    Invalid: ${integrity.invalid.length} images`);
console.log(`    Missing: ${integrity.missing.length} images`);

if (integrity.valid.length > 0) {
  console.log('    Valid images:', integrity.valid);
}
if (integrity.invalid.length > 0) {
  console.log('    Invalid images:', integrity.invalid);
}
if (integrity.missing.length > 0) {
  console.log('    Missing images:', integrity.missing);
}

// Test 4: Simulate update scenario
console.log('\n4. Simulating Campaign Update Scenario:');
const currentImages = [
  '/uploads/campaign123/old1.jpg',
  '/uploads/campaign123/old2.jpg',
  '/uploads/campaign123/old3.jpg'
];

const keptImages = [
  '/uploads/campaign123/old1.jpg',
  '/uploads/campaign123/old3.jpg'
];

const newImages = [
  '/uploads/campaign123/new1.jpg',
  '/uploads/campaign123/new2.jpg'
];

const imagesToDelete = currentImages.filter(img => !keptImages.includes(img));
const finalImages = [...keptImages, ...newImages];

console.log('  Current images:', currentImages);
console.log('  Images to keep:', keptImages);
console.log('  New images:', newImages);
console.log('  Images to delete:', imagesToDelete);
console.log('  Final images:', finalImages);

console.log('\n✅ Image Management System Test Complete!');
console.log('\nKey Features Tested:');
console.log('- ✅ Path normalization (Windows/Unix compatibility)');
console.log('- ✅ Unique filename generation');
console.log('- ✅ Image integrity verification');
console.log('- ✅ Smart update logic (keep + add + delete)');
console.log('- ✅ Error handling and validation');
